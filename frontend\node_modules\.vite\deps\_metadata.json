{"hash": "3eb823d5", "browserHash": "9786c0f3", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "32c3b55a", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "2e122d87", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "a6bd27e5", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "6e31e595", "needsInterop": true}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "311bce17", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "ff966095", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "5c3573bd", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "58bf0ef3", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "88017a21", "needsInterop": false}}, "chunks": {"chunk-ONHTVOXO": {"file": "chunk-ONHTVOXO.js"}, "chunk-NOXGKGVU": {"file": "chunk-NOXGKGVU.js"}, "chunk-UV5CTPV7": {"file": "chunk-UV5CTPV7.js"}}}