import { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useParams, useNavigate, Link } from 'react-router-dom'
import { fetchPost, deletePost, createComment, clearCurrentPost } from '../redux/slices/postsSlice'

const PostDetail = () => {
  const { id } = useParams()
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { currentPost, isLoading, error, deleteLoading } = useSelector(state => state.posts)
  const { user, isAuthenticated } = useSelector(state => state.auth)
  const [comment, setComment] = useState('')
  const [commentLoading, setCommentLoading] = useState(false)

  useEffect(() => {
    dispatch(fetchPost(id))
    return () => {
      dispatch(clearCurrentPost())
    }
  }, [dispatch, id])

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this post?')) {
      const result = await dispatch(deletePost(id))
      if (result.type === 'posts/deletePost/fulfilled') {
        navigate('/')
      }
    }
  }

  const handleCommentSubmit = async (e) => {
    e.preventDefault()
    if (!comment.trim()) return
    
    setCommentLoading(true)
    const result = await dispatch(createComment({ postId: id, content: comment }))
    if (result.type === 'posts/createComment/fulfilled') {
      setComment('')
    }
    setCommentLoading(false)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isLoading) {
    return <div className="loading">Loading post...</div>
  }

  if (error) {
    return <div className="error-message">Error: {error}</div>
  }

  if (!currentPost) {
    return <div className="error-message">Post not found</div>
  }

  const isAuthor = user && currentPost.author.id === user.id

  return (
    <div className="post-detail-container">
      <article className="post-detail">
        <header className="post-header">
          <h1>{currentPost.title}</h1>
          <div className="post-meta">
            <div className="author-info">
              <span className="author-name">
                By {currentPost.author.first_name} {currentPost.author.last_name}
              </span>
              <span className="author-username">({currentPost.author.username})</span>
            </div>
            <div className="post-dates">
              <span className="created-date">
                Created: {formatDate(currentPost.created_at)}
              </span>
              {currentPost.updated_at !== currentPost.created_at && (
                <span className="updated-date">
                  Updated: {formatDate(currentPost.updated_at)}
                </span>
              )}
            </div>
          </div>
          
          {isAuthor && (
            <div className="post-actions">
              <Link to={`/posts/${currentPost.id}/edit`} className="btn-secondary">
                Edit Post
              </Link>
              <button 
                onClick={handleDelete} 
                disabled={deleteLoading}
                className="btn-danger"
              >
                {deleteLoading ? 'Deleting...' : 'Delete Post'}
              </button>
            </div>
          )}
        </header>

        <div className="post-content">
          <p>{currentPost.content}</p>
        </div>
      </article>

      <section className="comments-section">
        <h3>Comments ({currentPost.comments_count})</h3>
        
        {isAuthenticated && (
          <form onSubmit={handleCommentSubmit} className="comment-form">
            <div className="form-group">
              <label htmlFor="comment">Add a comment:</label>
              <textarea
                id="comment"
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                placeholder="Write your comment here..."
                rows="4"
                required
              />
            </div>
            <button type="submit" disabled={commentLoading} className="btn-primary">
              {commentLoading ? 'Posting...' : 'Post Comment'}
            </button>
          </form>
        )}

        <div className="comments-list">
          {currentPost.comments.length === 0 ? (
            <p className="no-comments">No comments yet. Be the first to comment!</p>
          ) : (
            currentPost.comments.map(comment => (
              <div key={comment.id} className="comment">
                <div className="comment-header">
                  <span className="comment-author">
                    {comment.author.first_name} {comment.author.last_name}
                  </span>
                  <span className="comment-date">
                    {formatDate(comment.created_at)}
                  </span>
                </div>
                <div className="comment-content">
                  <p>{comment.content}</p>
                </div>
              </div>
            ))
          )}
        </div>
      </section>

      <div className="back-link">
        <Link to="/" className="btn-secondary">← Back to Posts</Link>
      </div>
    </div>
  )
}

export default PostDetail
