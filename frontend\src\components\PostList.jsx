import { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Link } from 'react-router-dom'
import { fetchPosts } from '../redux/slices/postsSlice'

const PostList = () => {
  const dispatch = useDispatch()
  const { posts, isLoading, error } = useSelector(state => state.posts)

  useEffect(() => {
    dispatch(fetchPosts())
  }, [dispatch])

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isLoading) {
    return <div className="loading">Loading posts...</div>
  }

  if (error) {
    return <div className="error-message">Error: {error}</div>
  }

  return (
    <div className="posts-container">
      <div className="posts-header">
        <h1>Latest Blog Posts</h1>
        <Link to="/create-post" className="btn-primary">
          Create New Post
        </Link>
      </div>
      
      {posts.length === 0 ? (
        <div className="no-posts">
          <p>No posts available. Be the first to create one!</p>
          <Link to="/create-post" className="btn-primary">
            Create Post
          </Link>
        </div>
      ) : (
        <div className="posts-grid">
          {posts.map(post => (
            <article key={post.id} className="post-card">
              <div className="post-header">
                <h2>
                  <Link to={`/posts/${post.id}`} className="post-title-link">
                    {post.title}
                  </Link>
                </h2>
                <div className="post-meta">
                  <span className="post-author">
                    By {post.author.first_name} {post.author.last_name} 
                    ({post.author.username})
                  </span>
                  <span className="post-date">
                    {formatDate(post.created_at)}
                  </span>
                </div>
              </div>
              
              <div className="post-content">
                <p>{post.content.substring(0, 200)}...</p>
              </div>
              
              <div className="post-footer">
                <div className="post-stats">
                  <span className="comments-count">
                    {post.comments_count} comment{post.comments_count !== 1 ? 's' : ''}
                  </span>
                </div>
                <Link to={`/posts/${post.id}`} className="read-more">
                  Read More →
                </Link>
              </div>
            </article>
          ))}
        </div>
      )}
    </div>
  )
}

export default PostList
