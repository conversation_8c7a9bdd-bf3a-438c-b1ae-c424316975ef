import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { Provider } from 'react-redux'
import { store } from './redux/store'
import Layout from './components/Layout'
import PostList from './components/PostList'
import PostDetail from './components/PostDetail'
import PostForm from './components/PostForm'
import Login from './components/Login'
import Register from './components/Register'
import './styles/App.css'

function App() {
  return (
    <Provider store={store}>
      <Router>
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<PostList />} />
            <Route path="posts/:id" element={<PostDetail />} />
            <Route path="posts/:id/edit" element={<PostForm isEdit={true} />} />
            <Route path="create-post" element={<PostForm />} />
            <Route path="login" element={<Login />} />
            <Route path="register" element={<Register />} />
          </Route>
        </Routes>
      </Router>
    </Provider>
  )
}

export default App
