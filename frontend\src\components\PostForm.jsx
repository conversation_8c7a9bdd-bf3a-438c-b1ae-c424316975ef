import { useState, useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useNavigate, useParams } from 'react-router-dom'
import { createPost, updatePost, fetchPost, clearCurrentPost } from '../redux/slices/postsSlice'

const PostForm = ({ isEdit = false }) => {
  const { id } = useParams()
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const { currentPost, createLoading, updateLoading, error } = useSelector(state => state.posts)
  const { isAuthenticated } = useSelector(state => state.auth)

  const [formData, setFormData] = useState({
    title: '',
    content: ''
  })

  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login')
      return
    }

    if (isEdit && id) {
      dispatch(fetchPost(id))
    }

    return () => {
      if (isEdit) {
        dispatch(clearCurrentPost())
      }
    }
  }, [dispatch, id, isEdit, isAuthenticated, navigate])

  useEffect(() => {
    if (isEdit && currentPost) {
      setFormData({
        title: currentPost.title,
        content: currentPost.content
      })
    }
  }, [currentPost, isEdit])

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    let result
    if (isEdit) {
      result = await dispatch(updatePost({ postId: id, postData: formData }))
    } else {
      result = await dispatch(createPost(formData))
    }

    if (result.type.endsWith('/fulfilled')) {
      if (isEdit) {
        navigate(`/posts/${id}`)
      } else {
        navigate('/')
      }
    }
  }

  const isLoading = createLoading || updateLoading

  return (
    <div className="post-form-container">
      <div className="post-form-card">
        <h2>{isEdit ? 'Edit Post' : 'Create New Post'}</h2>
        
        {error && (
          <div className="error-message">
            {typeof error === 'object' ? (
              Object.entries(error).map(([key, value]) => (
                <div key={key}>
                  <strong>{key}:</strong> {Array.isArray(value) ? value.join(', ') : value}
                </div>
              ))
            ) : (
              error
            )}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="title">Title</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              placeholder="Enter post title..."
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="content">Content</label>
            <textarea
              id="content"
              name="content"
              value={formData.content}
              onChange={handleChange}
              placeholder="Write your post content here..."
              rows="15"
              required
            />
          </div>

          <div className="form-actions">
            <button 
              type="button" 
              onClick={() => navigate(isEdit ? `/posts/${id}` : '/')}
              className="btn-secondary"
            >
              Cancel
            </button>
            <button type="submit" disabled={isLoading} className="btn-primary">
              {isLoading ? (isEdit ? 'Updating...' : 'Creating...') : (isEdit ? 'Update Post' : 'Create Post')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default PostForm
