import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import axios from 'axios'

// Async thunks
export const fetchPosts = createAsyncThunk(
  'posts/fetchPosts',
  async (_, { rejectWithValue }) => {
    try {
      const response = await axios.get('/posts/')
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch posts')
    }
  }
)

export const fetchPost = createAsyncThunk(
  'posts/fetchPost',
  async (postId, { rejectWithValue }) => {
    try {
      const response = await axios.get(`/posts/${postId}/`)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to fetch post')
    }
  }
)

export const createPost = createAsyncThunk(
  'posts/createPost',
  async (postData, { rejectWithValue }) => {
    try {
      const response = await axios.post('/posts/', postData)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to create post')
    }
  }
)

export const updatePost = createAsyncThunk(
  'posts/updatePost',
  async ({ postId, postData }, { rejectWithValue }) => {
    try {
      const response = await axios.put(`/posts/${postId}/`, postData)
      return response.data
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to update post')
    }
  }
)

export const deletePost = createAsyncThunk(
  'posts/deletePost',
  async (postId, { rejectWithValue }) => {
    try {
      await axios.delete(`/posts/${postId}/`)
      return postId
    } catch (error) {
      return rejectWithValue(error.response?.data?.detail || 'Failed to delete post')
    }
  }
)

export const createComment = createAsyncThunk(
  'posts/createComment',
  async ({ postId, content }, { rejectWithValue }) => {
    try {
      const response = await axios.post(`/posts/${postId}/comments/`, { content })
      return { postId, comment: response.data }
    } catch (error) {
      return rejectWithValue(error.response?.data || 'Failed to create comment')
    }
  }
)

const initialState = {
  posts: [],
  currentPost: null,
  isLoading: false,
  error: null,
  createLoading: false,
  updateLoading: false,
  deleteLoading: false
}

const postsSlice = createSlice({
  name: 'posts',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    clearCurrentPost: (state) => {
      state.currentPost = null
    }
  },
  extraReducers: (builder) => {
    builder
      // Fetch Posts
      .addCase(fetchPosts.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchPosts.fulfilled, (state, action) => {
        state.isLoading = false
        state.posts = action.payload.results || action.payload
        state.error = null
      })
      .addCase(fetchPosts.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      // Fetch Single Post
      .addCase(fetchPost.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchPost.fulfilled, (state, action) => {
        state.isLoading = false
        state.currentPost = action.payload
        state.error = null
      })
      .addCase(fetchPost.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      // Create Post
      .addCase(createPost.pending, (state) => {
        state.createLoading = true
        state.error = null
      })
      .addCase(createPost.fulfilled, (state, action) => {
        state.createLoading = false
        state.posts.unshift(action.payload)
        state.error = null
      })
      .addCase(createPost.rejected, (state, action) => {
        state.createLoading = false
        state.error = action.payload
      })
      // Update Post
      .addCase(updatePost.pending, (state) => {
        state.updateLoading = true
        state.error = null
      })
      .addCase(updatePost.fulfilled, (state, action) => {
        state.updateLoading = false
        const index = state.posts.findIndex(post => post.id === action.payload.id)
        if (index !== -1) {
          state.posts[index] = action.payload
        }
        if (state.currentPost && state.currentPost.id === action.payload.id) {
          state.currentPost = action.payload
        }
        state.error = null
      })
      .addCase(updatePost.rejected, (state, action) => {
        state.updateLoading = false
        state.error = action.payload
      })
      // Delete Post
      .addCase(deletePost.pending, (state) => {
        state.deleteLoading = true
        state.error = null
      })
      .addCase(deletePost.fulfilled, (state, action) => {
        state.deleteLoading = false
        state.posts = state.posts.filter(post => post.id !== action.payload)
        if (state.currentPost && state.currentPost.id === action.payload) {
          state.currentPost = null
        }
        state.error = null
      })
      .addCase(deletePost.rejected, (state, action) => {
        state.deleteLoading = false
        state.error = action.payload
      })
      // Create Comment
      .addCase(createComment.fulfilled, (state, action) => {
        const { postId, comment } = action.payload
        if (state.currentPost && state.currentPost.id === postId) {
          state.currentPost.comments.unshift(comment)
          state.currentPost.comments_count += 1
        }
        const postIndex = state.posts.findIndex(post => post.id === postId)
        if (postIndex !== -1) {
          state.posts[postIndex].comments_count += 1
        }
      })
  }
})

export const { clearError, clearCurrentPost } = postsSlice.actions
export default postsSlice.reducer
